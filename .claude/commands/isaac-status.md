# /isaac-status

**Quick status check without full dashboard**

## Description
Provides a quick, concise status overview of the current ISAAC session without the full dashboard display. Perfect for rapid progress checks and identifying immediate issues or blockers.

## Status Information
- **Session Progress**: Current task completion percentage
- **Active Branch**: Current git branch and status
- **GitHub Sync**: Issue and PR status
- **Immediate Issues**: Any blockers or problems
- **Next Steps**: What needs to be done next
- **Time Tracking**: Session duration and milestones

## Workflow
1. Show current session progress
2. Display any immediate issues or blockers
3. Quick GitHub sync status

## Quick Status Display
```
🚀 ISAAC SESSION STATUS
━━━━━━━━━━━━━━━━━━━━━━━━

📋 Task: Add user authentication flow
🌿 Branch: isaac/session-123-auth-flow
⏱️  Duration: 2h 15m
📊 Progress: 60% complete

✅ Completed:
   • User model created
   • Auth endpoints implemented
   
🔄 In Progress:
   • Frontend integration
   
📋 Remaining:
   • Unit tests
   • Integration tests

🔗 GitHub: Issue #42 (Open) | No PR yet
🔧 Status: On track, no blockers
```

## Usage
```bash
/isaac-status
```

## Status Indicators
- **🚀 Active**: Session running normally
- **⏸️ Paused**: Session paused/interrupted
- **⚠️ Issues**: Warnings or minor problems
- **❌ Blocked**: Critical issues preventing progress
- **✅ Complete**: Ready for `/isaac-end`

## Compared to Other Commands
- **vs `/isaac-progress`**: More detailed progress analysis
- **vs `/isaac-init`**: Full dashboard with all information
- **vs `/isaac-health`**: System health instead of session status

## When to Use
- Quick progress checks
- Before switching contexts
- Identifying blockers
- Checking sync status
- Rapid session overview

## Benefits
- **Fast**: Minimal output for quick consumption
- **Focused**: Only essential information
- **Non-intrusive**: Doesn't interrupt workflow
- **Actionable**: Highlights what needs attention

## Implementation
Executes the `isaac_status` protected pattern with streamlined status reporting optimized for quick consumption.